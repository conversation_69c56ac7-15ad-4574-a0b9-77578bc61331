const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function generatePDF() {
    console.log('🚀 Spúšťam generovanie PDF...');
    
    // Spustenie prehliadača
    const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    try {
        const page = await browser.newPage();
        
        // Načítanie HTML súboru
        const htmlPath = path.join(__dirname, 'index.html');
        const htmlContent = fs.readFileSync(htmlPath, 'utf8');
        
        console.log('📄 Načítavam HTML obsah...');
        
        // Nastavenie obsahu stránky
        await page.setContent(htmlContent, {
            waitUntil: ['networkidle0', 'domcontentloaded']
        });
        
        // Počkanie na načítanie fontov a obr<PERSON><PERSON>kov
        await page.evaluateHandle('document.fonts.ready');

        // Dodatočné čakanie pre istotu
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        console.log('🎨 Generujem PDF s vysokou kvalitou...');
        
        // Generovanie PDF s optimálnymi nastaveniami
        const pdfBuffer = await page.pdf({
            format: 'A4',
            printBackground: true,
            margin: {
                top: '20mm',
                right: '15mm',
                bottom: '20mm',
                left: '15mm'
            },
            displayHeaderFooter: false,
            preferCSSPageSize: false,
            scale: 0.8 // Mierne zmenšenie pre lepšie umiestnenie obsahu
        });
        
        // Uloženie PDF súboru
        const outputPath = path.join(__dirname, 'cenova-ponuka.pdf');
        fs.writeFileSync(outputPath, pdfBuffer);
        
        console.log('✅ PDF úspešne vytvorené!');
        console.log(`📁 Súbor uložený ako: ${outputPath}`);
        console.log(`📏 Veľkosť súboru: ${(pdfBuffer.length / 1024).toFixed(2)} KB`);
        
    } catch (error) {
        console.error('❌ Chyba pri generovaní PDF:', error);
        throw error;
    } finally {
        await browser.close();
    }
}

// Spustenie generátora
if (require.main === module) {
    generatePDF()
        .then(() => {
            console.log('🎉 Proces dokončený úspešne!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 Proces zlyhal:', error);
            process.exit(1);
        });
}

module.exports = { generatePDF };
