const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');

const pdfPath = path.join(__dirname, 'cenova-ponuka.pdf');

// Kontrola, či PDF existuje
if (!fs.existsSync(pdfPath)) {
    console.log('❌ PDF súbor neexistuje. Najprv spustite: npm run generate-pdf');
    process.exit(1);
}

console.log('📖 Otváram PDF súbor...');

// Otvorenie PDF v predvolenom prehliadači PDF
const command = process.platform === 'darwin' ? 'open' : 
               process.platform === 'win32' ? 'start' : 'xdg-open';

exec(`${command} "${pdfPath}"`, (error) => {
    if (error) {
        console.error('❌ Chyba pri otváraní PDF:', error.message);
        console.log(`📁 PDF súbor sa nachádza v: ${pdfPath}`);
    } else {
        console.log('✅ PDF súbor bol otvorený v predvolenom prehliadači');
    }
});
