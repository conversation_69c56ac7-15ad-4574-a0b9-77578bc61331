#!/usr/bin/env node
"use strict";
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const browsers_1 = require("@puppeteer/browsers");
const revisions_js_1 = require("puppeteer-core/internal/revisions.js");
const puppeteer_js_1 = __importDefault(require("../puppeteer.js"));
const cacheDir = puppeteer_js_1.default.configuration.cacheDirectory;
void new browsers_1.CLI({
    cachePath: cacheDir,
    scriptName: 'puppeteer',
    prefixCommand: {
        cmd: 'browsers',
        description: 'Manage browsers of this Puppeteer installation',
    },
    allowCachePathOverride: false,
    pinnedBrowsers: {
        [browsers_1.Browser.CHROME]: {
            buildId: puppeteer_js_1.default.configuration.browserRevision ||
                revisions_js_1.PUPPETEER_REVISIONS['chrome'] ||
                'latest',
            skipDownload: puppeteer_js_1.default.configuration.skipChromeDownload ?? false,
        },
        [browsers_1.Browser.FIREFOX]: {
            buildId: puppeteer_js_1.default.configuration.browserRevision ||
                revisions_js_1.PUPPETEER_REVISIONS['firefox'] ||
                'latest',
            skipDownload: true,
        },
        [browsers_1.Browser.CHROMEHEADLESSSHELL]: {
            buildId: puppeteer_js_1.default.configuration.browserRevision ||
                revisions_js_1.PUPPETEER_REVISIONS['chrome'] ||
                'latest',
            skipDownload: puppeteer_js_1.default.configuration.skipChromeHeadlessShellDownload ?? false,
        },
    },
}).run(process.argv);
//# sourceMappingURL=cli.js.map