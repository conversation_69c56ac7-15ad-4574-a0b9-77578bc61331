# Generátor PDF z HTML cenovej ponuky

Tento projekt umožňuje konvertovať HTML cenovú ponuku do PDF formátu s vysokou kvalitou a zachovaním všetkých vizuálnych prvkov.

## 📋 Obsah projektu

- `index.html` - HTML súbor s cenovou ponukou
- `generate-pdf.js` - JavaScript skript na generovanie PDF
- `package.json` - Konfigurácia Node.js projektu
- `cenova-ponuka.pdf` - Vygenerované PDF (vytvorí sa po spustení)

## 🚀 Inštalácia a spustenie

### Predpoklady
- Node.js (verzia 16 alebo vyššia)
- npm (Node Package Manager)

### Kroky na spustenie

1. **Inštalácia závislostí:**
   ```bash
   npm install
   ```

2. **Generovanie PDF:**
   ```bash
   npm run generate-pdf
   ```
   alebo
   ```bash
   npm start
   ```

3. **Výsledok:**
   - PDF súbor sa vytvorí ako `cenova-ponuka.pdf`
   - S<PERSON>bor bude obsahovať všetky vizuálne prvky z HTML

## 🎨 Funkcie

### HTML ponuka obsahuje:
- ✅ Profesionálny dizajn s Tailwind CSS
- ✅ Responzívne rozloženie
- ✅ Tabuľka s cenami služieb
- ✅ Detailný popis služieb
- ✅ Bonus sekcia
- ✅ Formulár na podpis klienta
- ✅ Kontaktné informácie

### PDF generátor:
- ✅ Zachováva všetky farby a štýly
- ✅ Optimalizované pre tlač (A4 formát)
- ✅ Vysoká kvalita obrázkov a fontov
- ✅ Správne okraje pre profesionálny vzhľad

## ⚙️ Technické detaily

- **Puppeteer**: Používa sa na konverziu HTML do PDF
- **Formát**: A4 s optimálnymi okrajmi
- **Fonty**: Google Fonts (Inter) pre profesionálny vzhľad
- **Farby**: Plne zachované v PDF výstupe

## 📝 Úprava obsahu

Pre úpravu obsahu cenovej ponuky editujte súbor `index.html`. Po úprave jednoducho spustite generovanie PDF znovu.

## 🔧 Riešenie problémov

Ak sa vyskytne chyba pri inštalácii Puppeteer:
```bash
npm install --unsafe-perm=true --allow-root
```

## 📞 Kontakt

**Vladimír Seman**
- Telefón: +421 951 553 464
- Email: <EMAIL>
- Web: www.ehroby.sk

---
*Vytvárame pokojné spomienky*
